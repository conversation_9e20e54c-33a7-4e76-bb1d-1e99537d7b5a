{"name": "typing-speed-tester", "version": "1.0.0", "description": "A comprehensive typing speed tester with customizable paragraphs, WPM tracking, and leaderboard functionality", "author": "<PERSON><PERSON> (chirag127)", "license": "MIT", "homepage": "https://github.com/chirag127/Typing-Speed-Tester", "repository": {"type": "git", "url": "https://github.com/chirag127/Typing-Speed-Tester.git"}, "keywords": ["typing", "speed", "test", "wpm", "accuracy", "nextjs", "typescript"], "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}